#!/usr/bin/env python3
"""
测试帧生产者事件循环修复
"""

import asyncio
import pytest
import logging
import time
from unittest.mock import MagicMock, patch
import numpy as np

from app.video.streams.frame_producer import FrameProducer
from app.video.streams.rtmp_connection_manager import RTMPConnectionManager
from app.utils.async_frame_queue import AsyncFrameQueue

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TestFrameProducerEventLoopFix:
    """测试帧生产者事件循环修复"""
    
    @pytest.fixture
    def mock_connection_manager(self):
        """创建模拟连接管理器"""
        manager = MagicMock(spec=RTMPConnectionManager)
        manager.is_connected.return_value = True
        
        # 创建模拟的VideoCapture对象
        mock_cap = MagicMock()
        mock_cap.isOpened.return_value = True
        mock_cap.read.return_value = (True, np.zeros((480, 640, 3), dtype=np.uint8))
        manager.get_video_capture.return_value = mock_cap
        
        return manager
    
    @pytest.fixture
    def frame_queue(self):
        """创建帧队列"""
        return AsyncFrameQueue(max_size=10, drop_old_frames=True)
    
    @pytest.mark.asyncio
    async def test_event_loop_setting(self, mock_connection_manager, frame_queue):
        """测试事件循环设置"""
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=frame_queue,
            stream_key="test_stream"
        )
        
        # 验证初始状态
        assert producer._loop is None
        
        # 设置事件循环
        current_loop = asyncio.get_running_loop()
        producer.set_event_loop(current_loop)
        
        # 验证事件循环已设置
        assert producer._loop is current_loop
        assert not producer._loop.is_closed()
    
    @pytest.mark.asyncio
    async def test_frame_production_with_event_loop(self, mock_connection_manager, frame_queue):
        """测试设置事件循环后的帧生产"""
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=frame_queue,
            stream_key="test_stream"
        )
        
        # 设置事件循环
        current_loop = asyncio.get_running_loop()
        producer.set_event_loop(current_loop)
        
        # 启动生产者
        success = producer.start_producing()
        assert success is True
        
        # 等待一些帧被生产
        await asyncio.sleep(0.5)
        
        # 检查队列中是否有帧
        assert frame_queue.get_queue_size() > 0
        
        # 停止生产者
        producer.stop_producing()
        assert producer.is_running() is False
    
    @pytest.mark.asyncio
    async def test_error_handling_with_invalid_loop(self, mock_connection_manager, frame_queue):
        """测试无效事件循环的错误处理"""
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=frame_queue,
            stream_key="test_stream"
        )
        
        # 创建一个已关闭的事件循环
        closed_loop = asyncio.new_event_loop()
        closed_loop.close()
        producer.set_event_loop(closed_loop)
        
        # 启动生产者
        success = producer.start_producing()
        assert success is True
        
        # 等待一段时间，应该会有错误日志但不会崩溃
        await asyncio.sleep(0.5)
        
        # 停止生产者
        producer.stop_producing()
    
    @pytest.mark.asyncio
    async def test_frame_production_without_event_loop(self, mock_connection_manager, frame_queue):
        """测试没有设置事件循环时的行为"""
        producer = FrameProducer(
            connection_manager=mock_connection_manager,
            frame_queue=frame_queue,
            stream_key="test_stream"
        )
        
        # 不设置事件循环，直接启动生产者
        success = producer.start_producing()
        assert success is True
        
        # 等待一段时间，应该会有错误日志
        await asyncio.sleep(0.5)
        
        # 停止生产者
        producer.stop_producing()
    
    @pytest.mark.asyncio
    async def test_multiple_producers_with_same_loop(self, mock_connection_manager):
        """测试多个生产者共享同一个事件循环"""
        current_loop = asyncio.get_running_loop()
        
        producers = []
        queues = []
        
        # 创建多个生产者
        for i in range(3):
            queue = AsyncFrameQueue(max_size=10, drop_old_frames=True)
            producer = FrameProducer(
                connection_manager=mock_connection_manager,
                frame_queue=queue,
                stream_key=f"test_stream_{i}"
            )
            producer.set_event_loop(current_loop)
            
            producers.append(producer)
            queues.append(queue)
        
        # 启动所有生产者
        for producer in producers:
            success = producer.start_producing()
            assert success is True
        
        # 等待帧生产
        await asyncio.sleep(0.5)
        
        # 检查所有队列都有帧
        for queue in queues:
            assert queue.get_queue_size() > 0
        
        # 停止所有生产者
        for producer in producers:
            producer.stop_producing()
            assert producer.is_running() is False

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
